import unittest
import os
import tempfile
from unittest.mock import Mock, patch, MagicMock
import sys

# Add the backend directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from agents.multimodal_rag_agent import MultiModalRAGAgent
from agents.multimodal_rag_agent.input_handler import MultiModalInputHandler, MultiModalInput
from agents.multimodal_rag_agent.routing_logic import Confidence<PERSON><PERSON>d<PERSON><PERSON><PERSON>, RoutingDecision, AgentType
from config import Config

class TestMultiModalRAGAgent(unittest.TestCase):
    """Test cases for the Multi-Modal RAG Agent."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create a mock configuration
        self.config = Mock()
        self.config.multimodal_rag = Mock()
        self.config.multimodal_rag.rag_confidence_threshold = 0.3
        self.config.multimodal_rag.image_confidence_threshold = 0.7
        self.config.multimodal_rag.supported_image_types = ["BRAIN MRI SCAN", "CHEST X-RAY", "SKIN LESION"]
        self.config.multimodal_rag.allow_non_medical_images = False
        self.config.multimodal_rag.enable_image_text_fusion = True
        self.config.multimodal_rag.max_retry_attempts = 2
        self.config.multimodal_rag.combine_responses = True
        self.config.multimodal_rag.response_priority = "rag"
        
        # Mock RAG config
        self.config.rag = Mock()
        self.config.rag.context_limit = 20
        
        # Create temporary image file for testing
        self.temp_image = tempfile.NamedTemporaryFile(suffix='.jpg', delete=False)
        self.temp_image.write(b'fake_image_data')
        self.temp_image.close()
        self.temp_image_path = self.temp_image.name
    
    def tearDown(self):
        """Clean up test fixtures."""
        # Remove temporary image file
        if os.path.exists(self.temp_image_path):
            os.unlink(self.temp_image_path)
    
    @patch('agents.multimodal_rag_agent.MedicalRAG')
    @patch('agents.multimodal_rag_agent.WebSearchProcessorAgent')
    @patch('agents.multimodal_rag_agent.ImageAnalysisAgent')
    def test_multimodal_agent_initialization(self, mock_image_agent, mock_web_agent, mock_rag_agent):
        """Test that the multi-modal agent initializes correctly."""
        agent = MultiModalRAGAgent(self.config)
        
        self.assertIsNotNone(agent.rag_agent)
        self.assertIsNotNone(agent.web_search_agent)
        self.assertIsNotNone(agent.image_analyzer)
        self.assertIsNotNone(agent.input_handler)
        self.assertIsNotNone(agent.router)
    
    def test_input_handler_text_only(self):
        """Test input handler with text-only input."""
        mock_image_analyzer = Mock()
        handler = MultiModalInputHandler(self.config, mock_image_analyzer)
        
        result = handler.process_input("What is a brain tumor?")
        
        self.assertIsInstance(result, MultiModalInput)
        self.assertEqual(result.text, "What is a brain tumor?")
        self.assertFalse(result.has_image)
        self.assertIsNone(result.image_path)
        self.assertIsNone(result.image_type)
    
    def test_input_handler_multimodal_input(self):
        """Test input handler with both text and image."""
        mock_image_analyzer = Mock()
        mock_image_analyzer.analyze_image.return_value = {
            "image_type": "BRAIN MRI SCAN",
            "confidence": 0.9,
            "reasoning": "High confidence brain MRI detection"
        }
        
        handler = MultiModalInputHandler(self.config, mock_image_analyzer)
        
        input_data = {
            "text": "What do you see in this brain scan?",
            "image": self.temp_image_path
        }
        
        result = handler.process_input(input_data)
        
        self.assertIsInstance(result, MultiModalInput)
        self.assertEqual(result.text, "What do you see in this brain scan?")
        self.assertTrue(result.has_image)
        self.assertEqual(result.image_path, self.temp_image_path)
        self.assertEqual(result.image_type, "BRAIN MRI SCAN")
        self.assertEqual(result.image_confidence, 0.9)
    
    def test_input_handler_image_validation(self):
        """Test input handler image validation."""
        mock_image_analyzer = Mock()
        handler = MultiModalInputHandler(self.config, mock_image_analyzer)
        
        # Test with non-existent image
        result = handler._analyze_image("/non/existent/path.jpg")
        self.assertEqual(result["image_type"], "UNKNOWN")
        self.assertEqual(result["confidence"], 0.0)
        self.assertIn("not found", result["reasoning"])
    
    def test_routing_logic_text_only(self):
        """Test routing logic for text-only input."""
        router = ConfidenceBasedRouter(self.config)
        
        multimodal_input = MultiModalInput(
            text="What is a brain tumor?",
            has_image=False
        )
        
        decision = router.route_request(multimodal_input)
        
        self.assertEqual(decision.primary_agent, AgentType.RAG)
        self.assertEqual(decision.fallback_agent, AgentType.WEB_SEARCH)
    
    def test_routing_logic_high_confidence_image(self):
        """Test routing logic for high confidence medical image."""
        router = ConfidenceBasedRouter(self.config)
        
        multimodal_input = MultiModalInput(
            text="Analyze this brain scan",
            image_path=self.temp_image_path,
            image_type="BRAIN MRI SCAN",
            image_confidence=0.9,
            has_image=True
        )
        
        decision = router.route_request(multimodal_input)
        
        # Should combine responses when configured
        self.assertTrue(decision.should_combine)
        self.assertIn(AgentType.RAG, decision.combination_agents)
        self.assertIn(AgentType.BRAIN_TUMOR, decision.combination_agents)
    
    def test_routing_logic_low_confidence_image(self):
        """Test routing logic for low confidence image."""
        router = ConfidenceBasedRouter(self.config)
        
        multimodal_input = MultiModalInput(
            text="What is this image?",
            image_path=self.temp_image_path,
            image_type="UNKNOWN",
            image_confidence=0.3,
            has_image=True
        )
        
        decision = router.route_request(multimodal_input)
        
        # Should fallback to text processing
        self.assertEqual(decision.primary_agent, AgentType.RAG)
        self.assertEqual(decision.fallback_agent, AgentType.WEB_SEARCH)
    
    def test_confidence_based_fallback(self):
        """Test confidence-based fallback to web search."""
        router = ConfidenceBasedRouter(self.config)
        
        # Test with low RAG confidence
        self.assertTrue(router.should_fallback_to_web_search(0.2, "Some response"))
        
        # Test with insufficient information response
        self.assertTrue(router.should_fallback_to_web_search(
            0.5, "I don't have enough information to answer this question"
        ))
        
        # Test with high confidence and good response
        self.assertFalse(router.should_fallback_to_web_search(0.8, "Here is the answer"))
    
    def test_image_agent_mapping(self):
        """Test correct mapping of image types to agents."""
        router = ConfidenceBasedRouter(self.config)
        
        self.assertEqual(
            router._get_image_agent_for_type("BRAIN MRI SCAN"), 
            AgentType.BRAIN_TUMOR
        )
        self.assertEqual(
            router._get_image_agent_for_type("CHEST X-RAY"), 
            AgentType.CHEST_XRAY
        )
        self.assertEqual(
            router._get_image_agent_for_type("SKIN LESION"), 
            AgentType.SKIN_LESION
        )
        self.assertIsNone(router._get_image_agent_for_type("UNKNOWN"))
    
    def test_response_combination(self):
        """Test combining responses from multiple agents."""
        router = ConfidenceBasedRouter(self.config)
        
        responses = {
            AgentType.RAG: {
                "response": "Brain tumors are abnormal growths...",
                "sources": [{"title": "Medical Journal", "path": "/path/to/source"}],
                "confidence": 0.8,
                "processing_time": 1.5
            },
            AgentType.BRAIN_TUMOR: {
                "response": "Tumor detected with 85% confidence",
                "sources": [],
                "confidence": 0.85,
                "processing_time": 2.0
            }
        }
        
        combined = router.combine_agent_responses(responses)
        
        self.assertIn("RAG_AGENT Analysis", combined["response"])
        self.assertIn("BRAIN_TUMOR_AGENT Analysis", combined["response"])
        self.assertEqual(combined["confidence"], 0.85)  # Max confidence
        self.assertEqual(combined["processing_time"], 3.5)  # Sum of times
        self.assertEqual(len(combined["sources"]), 1)
        self.assertEqual(len(combined["agents_used"]), 2)
    
    @patch('agents.multimodal_rag_agent.MedicalRAG')
    @patch('agents.multimodal_rag_agent.WebSearchProcessorAgent')
    @patch('agents.multimodal_rag_agent.ImageAnalysisAgent')
    def test_error_handling_in_agent_execution(self, mock_image_agent, mock_web_agent, mock_rag_agent):
        """Test error handling when agents fail."""
        # Mock RAG agent to raise an exception
        mock_rag_instance = Mock()
        mock_rag_instance.process_query.side_effect = Exception("RAG agent failed")
        mock_rag_agent.return_value = mock_rag_instance
        
        # Mock other agents
        mock_web_agent.return_value = Mock()
        mock_image_agent.return_value = Mock()
        
        agent = MultiModalRAGAgent(self.config)
        
        # Test that the agent handles the error gracefully
        result = agent.process_query("What is a brain tumor?")
        
        self.assertIn("error", result["response"].lower())
        self.assertEqual(result["confidence"], 0.0)
    
    def test_input_type_description(self):
        """Test input type description generation."""
        mock_image_analyzer = Mock()
        mock_rag_agent = Mock()
        mock_web_agent = Mock()
        
        with patch('agents.multimodal_rag_agent.MedicalRAG', return_value=mock_rag_agent), \
             patch('agents.multimodal_rag_agent.WebSearchProcessorAgent', return_value=mock_web_agent), \
             patch('agents.multimodal_rag_agent.ImageAnalysisAgent', return_value=mock_image_analyzer):
            
            agent = MultiModalRAGAgent(self.config)
            
            # Test text only
            text_input = MultiModalInput(text="Hello", has_image=False)
            desc = agent._get_input_type_description(text_input)
            self.assertEqual(desc, "text_only")
            
            # Test image only
            image_input = MultiModalInput(
                text="", 
                has_image=True, 
                image_type="BRAIN MRI SCAN"
            )
            desc = agent._get_input_type_description(image_input)
            self.assertEqual(desc, "image_only_BRAIN MRI SCAN")
            
            # Test multimodal
            multimodal_input = MultiModalInput(
                text="Analyze this", 
                has_image=True, 
                image_type="CHEST X-RAY"
            )
            desc = agent._get_input_type_description(multimodal_input)
            self.assertEqual(desc, "text_and_image_CHEST X-RAY")

if __name__ == '__main__':
    unittest.main()
