import logging
from typing import Dict, Any, Optional, List, Tuple
from enum import Enum
from dataclasses import dataclass

class AgentType(Enum):
    """Enumeration of available agent types."""
    RAG = "RAG_AGENT"
    WEB_SEARCH = "WEB_SEARCH_PROCESSOR_AGENT"
    IMAGE = "IMAGE_AGENT"
    BRAIN_TUMOR = "BRAIN_TUMOR_AGENT"
    CHEST_XRAY = "CHEST_XRAY_AGENT"
    SKIN_LESION = "SKIN_LESION_AGENT"

@dataclass
class RoutingDecision:
    """Represents a routing decision with confidence and reasoning."""
    primary_agent: AgentType
    fallback_agent: Optional[AgentType] = None
    confidence: float = 0.0
    reasoning: str = ""
    should_combine: bool = False
    combination_agents: List[AgentType] = None

class ConfidenceBasedRouter:
    """
    Handles routing decisions based on confidence scores and input characteristics.
    """
    
    def __init__(self, config):
        """
        Initialize the confidence-based router.
        
        Args:
            config: Configuration object containing routing thresholds
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Get configuration thresholds
        self.rag_confidence_threshold = config.multimodal_rag.rag_confidence_threshold
        self.image_confidence_threshold = config.multimodal_rag.image_confidence_threshold
        self.combine_responses = config.multimodal_rag.combine_responses
        self.response_priority = config.multimodal_rag.response_priority
    
    def route_request(self, multimodal_input, rag_confidence: Optional[float] = None) -> RoutingDecision:
        """
        Make routing decision based on input characteristics and confidence scores.
        
        Args:
            multimodal_input: MultiModalInput object with processed input data
            rag_confidence: Optional confidence score from RAG agent (if already executed)
            
        Returns:
            RoutingDecision object with routing information
        """
        try:
            # Determine primary routing strategy
            if multimodal_input.has_image and multimodal_input.text:
                return self._route_multimodal_input(multimodal_input, rag_confidence)
            elif multimodal_input.has_image:
                return self._route_image_only_input(multimodal_input)
            else:
                return self._route_text_only_input(multimodal_input, rag_confidence)
                
        except Exception as e:
            self.logger.error(f"Error in routing decision: {e}")
            # Fallback to RAG agent with web search fallback
            return RoutingDecision(
                primary_agent=AgentType.RAG,
                fallback_agent=AgentType.WEB_SEARCH,
                confidence=0.0,
                reasoning=f"Error in routing, using fallback: {str(e)}"
            )
    
    def _route_multimodal_input(self, multimodal_input, rag_confidence: Optional[float]) -> RoutingDecision:
        """
        Route input that contains both text and image.
        
        Args:
            multimodal_input: MultiModalInput with both text and image
            rag_confidence: Optional RAG confidence score
            
        Returns:
            RoutingDecision for multimodal input
        """
        image_agent = self._get_image_agent_for_type(multimodal_input.image_type)
        
        # Check if image is processable by specialized agents
        if (image_agent and 
            multimodal_input.image_confidence and 
            multimodal_input.image_confidence >= self.image_confidence_threshold):
            
            # High confidence image with specialized agent available
            if self.combine_responses:
                # Combine image analysis with RAG
                combination_agents = [AgentType.RAG, image_agent]
                
                # Add web search fallback if RAG confidence is low
                if rag_confidence is not None and rag_confidence < self.rag_confidence_threshold:
                    combination_agents.append(AgentType.WEB_SEARCH)
                
                return RoutingDecision(
                    primary_agent=image_agent if self.response_priority == "image" else AgentType.RAG,
                    should_combine=True,
                    combination_agents=combination_agents,
                    confidence=max(multimodal_input.image_confidence, rag_confidence or 0.0),
                    reasoning="Combining image analysis with text processing for comprehensive response"
                )
            else:
                # Prioritize based on configuration
                if self.response_priority == "image":
                    return RoutingDecision(
                        primary_agent=image_agent,
                        fallback_agent=AgentType.RAG,
                        confidence=multimodal_input.image_confidence,
                        reasoning="Prioritizing image analysis for medical image input"
                    )
                else:
                    return RoutingDecision(
                        primary_agent=AgentType.RAG,
                        fallback_agent=image_agent,
                        confidence=rag_confidence or 0.5,
                        reasoning="Prioritizing RAG for text-based medical knowledge"
                    )
        else:
            # Image not processable or low confidence, focus on text
            return self._route_text_only_input(multimodal_input, rag_confidence)
    
    def _route_image_only_input(self, multimodal_input) -> RoutingDecision:
        """
        Route input that contains only an image.
        
        Args:
            multimodal_input: MultiModalInput with only image data
            
        Returns:
            RoutingDecision for image-only input
        """
        image_agent = self._get_image_agent_for_type(multimodal_input.image_type)
        
        if (image_agent and 
            multimodal_input.image_confidence and 
            multimodal_input.image_confidence >= self.image_confidence_threshold):
            
            return RoutingDecision(
                primary_agent=image_agent,
                confidence=multimodal_input.image_confidence,
                reasoning=f"High confidence {multimodal_input.image_type} image routed to specialized agent"
            )
        else:
            # Low confidence or unsupported image type
            return RoutingDecision(
                primary_agent=AgentType.RAG,
                fallback_agent=AgentType.WEB_SEARCH,
                confidence=0.3,  # Default low confidence
                reasoning="Image analysis uncertain, falling back to general medical knowledge"
            )
    
    def _route_text_only_input(self, multimodal_input, rag_confidence: Optional[float]) -> RoutingDecision:
        """
        Route input that contains only text.
        
        Args:
            multimodal_input: MultiModalInput with only text data
            rag_confidence: Optional RAG confidence score
            
        Returns:
            RoutingDecision for text-only input
        """
        # Start with RAG agent
        primary_agent = AgentType.RAG
        fallback_agent = AgentType.WEB_SEARCH
        confidence = rag_confidence or 0.5
        
        # Determine if web search fallback is needed
        if rag_confidence is not None and rag_confidence < self.rag_confidence_threshold:
            reasoning = f"RAG confidence ({rag_confidence:.2f}) below threshold, will fallback to web search"
        else:
            reasoning = "Text-based query routed to RAG agent for medical knowledge retrieval"
        
        return RoutingDecision(
            primary_agent=primary_agent,
            fallback_agent=fallback_agent,
            confidence=confidence,
            reasoning=reasoning
        )
    
    def _get_image_agent_for_type(self, image_type: Optional[str]) -> Optional[AgentType]:
        """
        Get the appropriate image agent for the given image type.
        
        Args:
            image_type: Classified image type
            
        Returns:
            AgentType for the appropriate image agent or None
        """
        if not image_type:
            return None
        
        image_agent_mapping = {
            "BRAIN MRI SCAN": AgentType.BRAIN_TUMOR,
            "CHEST X-RAY": AgentType.CHEST_XRAY,
            "SKIN LESION": AgentType.SKIN_LESION
        }
        
        return image_agent_mapping.get(image_type)
    
    def should_fallback_to_web_search(self, rag_confidence: float, rag_response: str) -> bool:
        """
        Determine if we should fallback to web search based on RAG results.
        
        Args:
            rag_confidence: Confidence score from RAG agent
            rag_response: Response text from RAG agent
            
        Returns:
            Boolean indicating if web search fallback is needed
        """
        # Check confidence threshold
        if rag_confidence < self.rag_confidence_threshold:
            return True
        
        # Check for insufficient information indicators in response
        insufficient_indicators = [
            "I don't have enough information",
            "I cannot answer",
            "insufficient information",
            "not available in the provided context",
            "I'm not sure",
            "I don't know"
        ]
        
        response_lower = rag_response.lower()
        for indicator in insufficient_indicators:
            if indicator in response_lower:
                return True
        
        return False
    
    def combine_agent_responses(self, responses: Dict[AgentType, Dict[str, Any]]) -> Dict[str, Any]:
        """
        Combine responses from multiple agents into a unified response.
        
        Args:
            responses: Dictionary mapping agent types to their responses
            
        Returns:
            Combined response dictionary
        """
        try:
            combined_response = {
                "response": "",
                "sources": [],
                "confidence": 0.0,
                "agents_used": [],
                "processing_time": 0.0
            }
            
            # Collect all responses and metadata
            response_parts = []
            all_sources = []
            confidences = []
            total_processing_time = 0.0
            
            for agent_type, response_data in responses.items():
                if response_data and response_data.get("response"):
                    response_parts.append(f"**{agent_type.value} Analysis:**\n{response_data['response']}")
                    
                    # Collect sources
                    if response_data.get("sources"):
                        all_sources.extend(response_data["sources"])
                    
                    # Collect confidence scores
                    if response_data.get("confidence") is not None:
                        confidences.append(response_data["confidence"])
                    
                    # Sum processing times
                    if response_data.get("processing_time"):
                        total_processing_time += response_data["processing_time"]
                    
                    combined_response["agents_used"].append(agent_type.value)
            
            # Combine response parts
            combined_response["response"] = "\n\n".join(response_parts)
            
            # Remove duplicate sources
            unique_sources = []
            seen_sources = set()
            for source in all_sources:
                source_key = f"{source.get('title', '')}|{source.get('path', '')}"
                if source_key not in seen_sources:
                    unique_sources.append(source)
                    seen_sources.add(source_key)
            
            combined_response["sources"] = unique_sources
            combined_response["confidence"] = max(confidences) if confidences else 0.0
            combined_response["processing_time"] = total_processing_time
            
            return combined_response
            
        except Exception as e:
            self.logger.error(f"Error combining agent responses: {e}")
            # Return the first available response as fallback
            for response_data in responses.values():
                if response_data and response_data.get("response"):
                    return response_data
            
            # Ultimate fallback
            return {
                "response": "Error combining responses from multiple agents.",
                "sources": [],
                "confidence": 0.0,
                "agents_used": [],
                "processing_time": 0.0
            }
