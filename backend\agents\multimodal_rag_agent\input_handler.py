import logging
from typing import Dict, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, List
from dataclasses import dataclass

@dataclass
class MultiModalInput:
    """Structured representation of multi-modal input."""
    text: str
    image_path: Optional[str] = None
    image_type: Optional[str] = None
    image_confidence: Optional[float] = None
    has_image: bool = False

class MultiModalInputHandler:
    """
    Handles processing and validation of multi-modal inputs containing both text and images.
    """
    
    def __init__(self, config, image_analyzer):
        """
        Initialize the multi-modal input handler.
        
        Args:
            config: Configuration object containing multi-modal RAG settings
            image_analyzer: Image analysis agent for classifying images
        """
        self.config = config
        self.image_analyzer = image_analyzer
        self.logger = logging.getLogger(__name__)
        
        # Get configuration settings
        self.supported_image_types = config.multimodal_rag.supported_image_types
        self.image_confidence_threshold = config.multimodal_rag.image_confidence_threshold
        self.allow_non_medical_images = config.multimodal_rag.allow_non_medical_images
    
    def process_input(self, current_input: Union[str, Dict[str, Any]]) -> MultiModalInput:
        """
        Process and validate multi-modal input.
        
        Args:
            current_input: Input that can be string (text only) or dict with text and image
            
        Returns:
            MultiModalInput object with processed and validated data
        """
        try:
            # Initialize default values
            text = ""
            image_path = None
            image_type = None
            image_confidence = None
            has_image = False
            
            # Extract text and image from input
            if isinstance(current_input, str):
                text = current_input
            elif isinstance(current_input, dict):
                text = current_input.get("text", "")
                image_path = current_input.get("image")
                
                if image_path:
                    has_image = True
                    # Analyze the image
                    image_analysis = self._analyze_image(image_path)
                    image_type = image_analysis.get("image_type")
                    image_confidence = image_analysis.get("confidence", 0.0)
            
            # Validate the processed input
            validation_result = self._validate_input(text, image_path, image_type, image_confidence)
            
            return MultiModalInput(
                text=text,
                image_path=image_path,
                image_type=image_type,
                image_confidence=image_confidence,
                has_image=has_image
            )
            
        except Exception as e:
            self.logger.error(f"Error processing multi-modal input: {e}")
            # Return a safe fallback with just text
            fallback_text = current_input if isinstance(current_input, str) else current_input.get("text", "")
            return MultiModalInput(
                text=fallback_text,
                image_path=None,
                image_type=None,
                image_confidence=None,
                has_image=False
            )
    
    def _analyze_image(self, image_path: str) -> Dict[str, Any]:
        """
        Analyze image using the image analyzer with comprehensive error handling.

        Args:
            image_path: Path to the image file

        Returns:
            Dictionary containing image analysis results
        """
        try:
            # Validate image path exists
            import os
            if not os.path.exists(image_path):
                self.logger.error(f"Image file not found: {image_path}")
                return {
                    "image_type": "UNKNOWN",
                    "confidence": 0.0,
                    "reasoning": "Image file not found"
                }

            # Check file size (basic validation)
            file_size = os.path.getsize(image_path)
            if file_size == 0:
                self.logger.error(f"Image file is empty: {image_path}")
                return {
                    "image_type": "UNKNOWN",
                    "confidence": 0.0,
                    "reasoning": "Image file is empty"
                }

            # Check if file is too large (>10MB)
            if file_size > 10 * 1024 * 1024:
                self.logger.warning(f"Large image file ({file_size} bytes): {image_path}")

            self.logger.info(f"Analyzing image: {image_path}")
            analysis_result = self.image_analyzer.analyze_image(image_path)

            # Ensure we have the expected format
            if isinstance(analysis_result, dict):
                # Validate required fields
                if "image_type" not in analysis_result:
                    analysis_result["image_type"] = "UNKNOWN"
                if "confidence" not in analysis_result:
                    analysis_result["confidence"] = 0.5
                if "reasoning" not in analysis_result:
                    analysis_result["reasoning"] = "Analysis completed"
                return analysis_result
            else:
                # Handle legacy string format
                return {
                    "image_type": str(analysis_result),
                    "confidence": 0.5,  # Default confidence for legacy format
                    "reasoning": "Legacy analysis format"
                }

        except FileNotFoundError:
            self.logger.error(f"Image file not found: {image_path}")
            return {
                "image_type": "UNKNOWN",
                "confidence": 0.0,
                "reasoning": "Image file not found"
            }
        except PermissionError:
            self.logger.error(f"Permission denied accessing image: {image_path}")
            return {
                "image_type": "UNKNOWN",
                "confidence": 0.0,
                "reasoning": "Permission denied accessing image file"
            }
        except Exception as e:
            self.logger.error(f"Error analyzing image {image_path}: {e}")
            return {
                "image_type": "UNKNOWN",
                "confidence": 0.0,
                "reasoning": f"Analysis failed: {str(e)}"
            }
    
    def _validate_input(self, text: str, image_path: Optional[str], 
                       image_type: Optional[str], image_confidence: Optional[float]) -> Dict[str, Any]:
        """
        Validate the processed input according to configuration rules.
        
        Args:
            text: Extracted text
            image_path: Path to image (if any)
            image_type: Classified image type
            image_confidence: Confidence score for image classification
            
        Returns:
            Dictionary containing validation results
        """
        validation_result = {
            "is_valid": True,
            "warnings": [],
            "errors": []
        }
        
        # Validate text input
        if not text or text.strip() == "":
            if not image_path:
                validation_result["errors"].append("No text or image provided")
                validation_result["is_valid"] = False
            else:
                validation_result["warnings"].append("No text provided, relying on image analysis only")
        
        # Validate image input if present
        if image_path:
            # Check image confidence
            if image_confidence is not None and image_confidence < self.image_confidence_threshold:
                validation_result["warnings"].append(
                    f"Low image classification confidence: {image_confidence:.2f}"
                )
            
            # Check if image type is supported
            if image_type and image_type not in self.supported_image_types:
                if image_type == "NON-MEDICAL" and not self.allow_non_medical_images:
                    validation_result["warnings"].append(
                        "Non-medical image detected - may not be processed by specialized agents"
                    )
                elif image_type not in ["NON-MEDICAL", "UNKNOWN"]:
                    validation_result["warnings"].append(
                        f"Unsupported medical image type: {image_type}"
                    )
        
        return validation_result
    
    def is_image_processable(self, image_type: Optional[str], image_confidence: Optional[float]) -> bool:
        """
        Determine if an image can be processed by the specialized image agents.
        
        Args:
            image_type: Classified image type
            image_confidence: Confidence score for image classification
            
        Returns:
            Boolean indicating if image is processable
        """
        if not image_type or not image_confidence:
            return False
        
        # Check confidence threshold
        if image_confidence < self.image_confidence_threshold:
            return False
        
        # Check if type is supported
        if image_type in self.supported_image_types:
            return True
        
        # Allow non-medical images if configured
        if image_type == "NON-MEDICAL" and self.allow_non_medical_images:
            return True
        
        return False
    
    def get_appropriate_image_agent(self, image_type: str) -> Optional[str]:
        """
        Determine which image agent should handle the given image type.
        
        Args:
            image_type: Classified image type
            
        Returns:
            Agent name or None if no appropriate agent
        """
        image_agent_mapping = {
            "BRAIN MRI SCAN": "BRAIN_TUMOR_AGENT",
            "CHEST X-RAY": "CHEST_XRAY_AGENT", 
            "SKIN LESION": "SKIN_LESION_AGENT"
        }
        
        return image_agent_mapping.get(image_type)
    
    def should_combine_with_rag(self, text: str, image_type: Optional[str]) -> bool:
        """
        Determine if the input should be processed by both RAG and image agents.
        
        Args:
            text: Input text
            image_type: Classified image type
            
        Returns:
            Boolean indicating if RAG should be combined with image processing
        """
        # If no meaningful text, don't use RAG
        if not text or text.strip() == "":
            return False
        
        # If image is not medical or unknown, rely more on RAG
        if not image_type or image_type in ["NON-MEDICAL", "UNKNOWN"]:
            return True
        
        # For medical images with text, combine both if configured
        return self.config.multimodal_rag.enable_image_text_fusion
