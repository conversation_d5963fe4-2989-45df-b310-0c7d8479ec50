import logging
import time
from typing import Dict, Any, Optional, List, Union

from .input_handler import MultiModalInputHandler, MultiModalInput
from .routing_logic import ConfidenceBasedRouter, RoutingDecision, AgentType

# Import existing agents
from ..rag_agent import MedicalRAG
from ..web_search_processor_agent import WebSearchProcessorAgent
from ..image_analysis_agent import ImageAnalysisAgent

class MultiModalRAGAgent:
    """
    Multi-modal RAG agent that orchestrates between RAG, image analysis, and web search agents
    based on input type and confidence scores.
    """
    
    def __init__(self, config):
        """
        Initialize the multi-modal RAG agent.
        
        Args:
            config: Configuration object containing all agent settings
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Initialize components
        self.input_handler = None
        self.router = ConfidenceBasedRouter(config)
        
        # Initialize existing agents with error handling
        try:
            self.rag_agent = MedicalRAG(config)
            self.web_search_agent = WebSearchProcessorAgent(config)
            self.image_analyzer = ImageAnalysisAgent(config)

            # Initialize input handler with image analyzer
            self.input_handler = MultiModalInputHandler(config, self.image_analyzer)

            self.logger.info("Multi-modal RAG agent initialized successfully")
        except Exception as e:
            self.logger.error(f"Error initializing multi-modal RAG agent: {e}")
            raise
    
    def process_query(self, current_input: Union[str, Dict[str, Any]], 
                     chat_history: Optional[List[Dict[str, str]]] = None) -> Dict[str, Any]:
        """
        Process a multi-modal query that may contain text and/or images.
        
        Args:
            current_input: Input that can be string (text only) or dict with text and image
            chat_history: Optional chat history for context
            
        Returns:
            Dictionary containing response and metadata
        """
        start_time = time.time()
        
        try:
            self.logger.info("Processing multi-modal query")
            
            # Step 1: Process and validate input
            multimodal_input = self.input_handler.process_input(current_input)
            self.logger.info(f"Input processed - Text: {bool(multimodal_input.text)}, "
                           f"Image: {multimodal_input.has_image}, "
                           f"Image Type: {multimodal_input.image_type}")
            
            # Step 2: Initial routing decision
            initial_routing = self.router.route_request(multimodal_input)
            self.logger.info(f"Initial routing: {initial_routing.primary_agent.value}")
            
            # Step 3: Execute agents based on routing decision
            if initial_routing.should_combine:
                response = self._execute_combined_agents(
                    multimodal_input, initial_routing, chat_history
                )
            else:
                response = self._execute_single_agent_flow(
                    multimodal_input, initial_routing, chat_history
                )
            
            # Add processing metadata
            response["processing_time"] = time.time() - start_time
            response["multimodal_input_type"] = self._get_input_type_description(multimodal_input)
            response["routing_decision"] = initial_routing.reasoning
            
            return response
            
        except Exception as e:
            self.logger.error(f"Error processing multi-modal query: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            
            # Return error response
            return {
                "response": f"I encountered an error while processing your query: {str(e)}",
                "sources": [],
                "confidence": 0.0,
                "processing_time": time.time() - start_time,
                "multimodal_input_type": "error",
                "routing_decision": f"Error occurred: {str(e)}"
            }
    
    def _execute_combined_agents(self, multimodal_input: MultiModalInput, 
                               routing: RoutingDecision, 
                               chat_history: Optional[List[Dict[str, str]]]) -> Dict[str, Any]:
        """
        Execute multiple agents and combine their responses.
        
        Args:
            multimodal_input: Processed input data
            routing: Routing decision with combination agents
            chat_history: Optional chat history
            
        Returns:
            Combined response from multiple agents
        """
        self.logger.info("Executing combined agents")
        responses = {}
        
        for agent_type in routing.combination_agents:
            try:
                if agent_type == AgentType.RAG:
                    if multimodal_input.text:
                        response = self.rag_agent.process_query(
                            multimodal_input.text, chat_history
                        )
                        responses[agent_type] = response
                        
                elif agent_type == AgentType.WEB_SEARCH:
                    if multimodal_input.text:
                        response = self.web_search_agent.process_web_search_results(
                            multimodal_input.text, chat_history
                        )
                        # Convert to expected format
                        responses[agent_type] = {
                            "response": response.content if hasattr(response, 'content') else str(response),
                            "sources": [],
                            "confidence": 0.7  # Default confidence for web search
                        }
                        
                elif agent_type in [AgentType.BRAIN_TUMOR, AgentType.CHEST_XRAY, AgentType.SKIN_LESION]:
                    if multimodal_input.has_image:
                        response = self._execute_image_agent(agent_type, multimodal_input.image_path)
                        responses[agent_type] = response
                        
            except Exception as e:
                self.logger.error(f"Error executing {agent_type.value}: {e}")
                continue
        
        # Combine responses
        return self.router.combine_agent_responses(responses)
    
    def _execute_single_agent_flow(self, multimodal_input: MultiModalInput, 
                                 routing: RoutingDecision, 
                                 chat_history: Optional[List[Dict[str, str]]]) -> Dict[str, Any]:
        """
        Execute single agent with potential fallback.
        
        Args:
            multimodal_input: Processed input data
            routing: Routing decision
            chat_history: Optional chat history
            
        Returns:
            Response from primary or fallback agent
        """
        self.logger.info(f"Executing single agent flow: {routing.primary_agent.value}")
        
        # Execute primary agent
        primary_response = self._execute_agent(
            routing.primary_agent, multimodal_input, chat_history
        )
        
        # Check if fallback is needed
        if routing.fallback_agent and self._should_use_fallback(primary_response, routing):
            self.logger.info(f"Using fallback agent: {routing.fallback_agent.value}")
            fallback_response = self._execute_agent(
                routing.fallback_agent, multimodal_input, chat_history
            )
            
            # Combine primary and fallback responses
            if fallback_response:
                return self._combine_primary_and_fallback(primary_response, fallback_response)
        
        return primary_response or self._get_error_response()
    
    def _execute_agent(self, agent_type: AgentType, multimodal_input: MultiModalInput, 
                      chat_history: Optional[List[Dict[str, str]]]) -> Optional[Dict[str, Any]]:
        """
        Execute a specific agent based on agent type.
        
        Args:
            agent_type: Type of agent to execute
            multimodal_input: Processed input data
            chat_history: Optional chat history
            
        Returns:
            Agent response or None if execution failed
        """
        try:
            if agent_type == AgentType.RAG:
                if multimodal_input.text:
                    return self.rag_agent.process_query(multimodal_input.text, chat_history)
                    
            elif agent_type == AgentType.WEB_SEARCH:
                if multimodal_input.text:
                    response = self.web_search_agent.process_web_search_results(
                        multimodal_input.text, chat_history
                    )
                    return {
                        "response": response.content if hasattr(response, 'content') else str(response),
                        "sources": [],
                        "confidence": 0.7
                    }
                    
            elif agent_type in [AgentType.BRAIN_TUMOR, AgentType.CHEST_XRAY, AgentType.SKIN_LESION]:
                if multimodal_input.has_image:
                    return self._execute_image_agent(agent_type, multimodal_input.image_path)
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error executing {agent_type.value}: {e}")
            return None
    
    def _execute_image_agent(self, agent_type: AgentType, image_path: str) -> Dict[str, Any]:
        """
        Execute image analysis agent.
        
        Args:
            agent_type: Type of image agent
            image_path: Path to image file
            
        Returns:
            Image analysis response
        """
        try:
            if agent_type == AgentType.BRAIN_TUMOR:
                from ..image_analysis_agent.brain_tumor_agent.brain_tumor_inference import BrainTumorInference
                analyzer = BrainTumorInference()
                result = analyzer.analyze_mri(image_path)
                return self._format_image_response(result, "Brain MRI")
                
            elif agent_type == AgentType.CHEST_XRAY:
                result = self.image_analyzer.classify_chest_xray(image_path)
                return self._format_image_response({"prediction": result}, "Chest X-ray")
                
            elif agent_type == AgentType.SKIN_LESION:
                result = self.image_analyzer.segment_skin_lesion(image_path)
                return self._format_image_response({"segmentation": result}, "Skin Lesion")
            
            return self._get_error_response("Unsupported image agent type")
            
        except Exception as e:
            self.logger.error(f"Error in image agent execution: {e}")
            return self._get_error_response(f"Image analysis failed: {str(e)}")
    
    def _format_image_response(self, result: Dict[str, Any], image_type: str) -> Dict[str, Any]:
        """Format image analysis result into standard response format."""
        try:
            if "error" in result:
                return self._get_error_response(result["error"])
            
            # Format response based on image type and result
            response_text = f"{image_type} Analysis Results:\n"
            
            if "has_tumor" in result:
                # Brain tumor analysis
                confidence = result.get("confidence", 0.0) * 100
                if result["has_tumor"]:
                    response_text += f"Tumor detected with {confidence:.1f}% confidence\n"
                    if result.get("tumor_type"):
                        response_text += f"Tumor type: {result['tumor_type']}\n"
                else:
                    response_text += f"No tumor detected with {confidence:.1f}% confidence\n"
                    
            elif "prediction" in result:
                # Chest X-ray or other classification
                response_text += f"Classification: {result['prediction']}\n"
                
            elif "segmentation" in result:
                # Skin lesion segmentation
                response_text += f"Segmentation completed: {result['segmentation']}\n"
            
            return {
                "response": response_text,
                "sources": [],
                "confidence": result.get("confidence", 0.7)
            }
            
        except Exception as e:
            return self._get_error_response(f"Error formatting image response: {str(e)}")
    
    def _should_use_fallback(self, primary_response: Optional[Dict[str, Any]], 
                           routing: RoutingDecision) -> bool:
        """Determine if fallback agent should be used."""
        if not primary_response:
            return True
        
        # Check confidence threshold for RAG responses
        if routing.primary_agent == AgentType.RAG:
            confidence = primary_response.get("confidence", 0.0)
            response_text = primary_response.get("response", "")
            return self.router.should_fallback_to_web_search(confidence, response_text)
        
        return False
    
    def _combine_primary_and_fallback(self, primary: Dict[str, Any], 
                                    fallback: Dict[str, Any]) -> Dict[str, Any]:
        """Combine primary and fallback responses."""
        combined_response = primary.get("response", "")
        if fallback.get("response"):
            combined_response += f"\n\n**Additional Information from Web Search:**\n{fallback['response']}"
        
        return {
            "response": combined_response,
            "sources": primary.get("sources", []) + fallback.get("sources", []),
            "confidence": max(primary.get("confidence", 0.0), fallback.get("confidence", 0.0)),
            "agents_used": ["PRIMARY", "FALLBACK"]
        }
    
    def _get_input_type_description(self, multimodal_input: MultiModalInput) -> str:
        """Get description of input type for metadata."""
        if multimodal_input.has_image and multimodal_input.text:
            return f"text_and_image_{multimodal_input.image_type or 'unknown'}"
        elif multimodal_input.has_image:
            return f"image_only_{multimodal_input.image_type or 'unknown'}"
        else:
            return "text_only"
    
    def _get_error_response(self, error_message: str = "An error occurred") -> Dict[str, Any]:
        """Get standardized error response."""
        return {
            "response": f"I apologize, but {error_message}. Please try rephrasing your question or uploading a different image.",
            "sources": [],
            "confidence": 0.0
        }
